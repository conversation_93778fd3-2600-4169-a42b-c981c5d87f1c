/* Font Imports */
@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.6;
    color: #112736;
    overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #fff8ef;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #4b879a, #112736);
    border-radius: 10px;
    border: 2px solid #fff8ef;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #112736, #04070f);
}

/* Header styles are now handled by header-and-footer.js */

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    padding-top: 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    min-height: 100vh;
}

.hero-text {
    background: #fff8ef;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px;
    position: relative;
}

.main-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: #4b879a; /* Petrol-Blau */
    margin-bottom: 20px;
    letter-spacing: 3px;
    line-height: 1.1;
}

.subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
    font-weight: 400;
    color: #112736;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.circle {
    color: #112736;
    font-size: 0.8em;
    font-weight: bold;
}

.hero-image {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 3px;
    animation: bounce 2s infinite;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: background-color 0.3s ease, opacity 0.3s ease;
    z-index: 10;
}

.scroll-indicator:hover {
    background-color: rgba(17, 39, 54, 0.1);
}

.scroll-arrow {
    width: 16px;
    height: 16px;
    border: none;
    border-right: 2px solid #112736;
    border-bottom: 2px solid #112736;
    transform: rotate(45deg);
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.scroll-indicator:hover .scroll-arrow {
    opacity: 1;
}

.scroll-arrow:nth-child(1) {
    animation-delay: 0s;
}

.scroll-arrow:nth-child(2) {
    animation-delay: 0.2s;
    margin-top: -8px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
        opacity: 0.7;
    }
    40% {
        transform: translateY(-8px);
        opacity: 1;
    }
    60% {
        transform: translateY(-4px);
        opacity: 0.9;
    }
}

/* Music Preview Section */
.music-preview-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fff8ef 0%, #4b879a 100%);
    padding: 4rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.music-preview-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.music-preview-text {
    text-align: left;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #112736;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.section-description {
    font-size: 1.2rem;
    color: #112736;
    font-weight: 300;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.music-highlights {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
}

.highlight-item {
    text-align: center;
}

.highlight-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #112736;
    margin-bottom: 0.5rem;
}

.highlight-text {
    font-size: 0.9rem;
    color: #112736;
    font-weight: 500;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.music-cta-button {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: #112736;
    color: #fff8ef;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(17, 39, 54, 0.3);
}

.music-cta-button:hover {
    background: #4b879a;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(17, 39, 54, 0.4);
}

.button-icon {
    font-size: 1.3rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Music Player Preview */
.music-preview-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.music-player-preview {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 300px;
    width: 100%;
}

.preview-album-cover {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    cursor: pointer;
    transition: transform 0.3s ease;
    width: 120px;
    height: 120px;
    overflow: hidden;
    margin-left: auto;
    margin-right: auto;
}

.preview-album-cover:hover {
    transform: scale(1.05);
}

.preview-album-cover img {
    width: 120px;
    height: 120px;
    max-width: 120px;
    max-height: 120px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    display: block;
}

.preview-cover-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    background: linear-gradient(135deg, #112736, #4b879a);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #fff8ef;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.preview-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #112736, #4b879a);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.preview-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.preview-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.preview-button svg {
    width: 20px;
    height: 20px;
}

.preview-progress {
    flex: 1;
    height: 6px;
    background: rgba(17, 39, 54, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.preview-progress-fill {
    height: 100%;
    background: #4b879a;
    border-radius: 3px;
    width: 60%;
    animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
    0%, 100% { width: 60%; }
    50% { width: 80%; }
}

.preview-song-info {
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
    padding: 0.5rem;
    border-radius: 8px;
}

.preview-song-info:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.5);
}

.preview-song-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #112736;
    margin-bottom: 0.25rem;
}

.preview-artist {
    font-size: 0.9rem;
    color: #112736;
    opacity: 0.7;
}

/* Responsive Design for Music Preview */
@media (max-width: 768px) {
    .music-preview-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .music-preview-text {
        text-align: center;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .section-description {
        font-size: 1.1rem;
    }

    .music-highlights {
        justify-content: center;
        gap: 1.5rem;
    }

    .highlight-number {
        font-size: 2rem;
    }

    .music-player-preview {
        max-width: 280px;
        padding: 1.5rem;
    }

    .preview-cover {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .music-preview-section {
        padding: 2rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .music-highlights {
        flex-direction: column;
        gap: 1rem;
    }

    .highlight-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .highlight-number {
        font-size: 1.8rem;
        margin-bottom: 0;
    }

    .music-cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .music-player-preview {
        max-width: 250px;
        padding: 1rem;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
    }
    
    .hero-text {
        padding: 40px 30px;
        text-align: center;
        align-items: center;
    }
    
    .main-title {
        font-size: clamp(2rem, 8vw, 3rem);
        margin-bottom: 15px;
    }
    
    .subtitle {
        font-size: clamp(1rem, 4vw, 1.4rem);
        justify-content: center;
        text-align: center;
    }
    
    .hero-image {
        min-height: 50vh;
    }
    
    .scroll-indicator {
        bottom: 20px;
    }
}

@media (max-width: 480px) {
    .hero-text {
        padding: 30px 20px;
    }

    .subtitle {
        gap: 10px;
    }
}

/* Impressum Page Styles */
.impressum-page {
    background: linear-gradient(135deg, #fff8ef 0%, #4b879a 100%);
    min-height: 100vh;
    padding-top: 80px;
}

.impressum-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    background: #fff8ef;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(4,7,15,0.1);
    margin-top: 40px;
    margin-bottom: 40px;
}

.impressum-content h2 {
    color: #112736;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 30px;
    margin-top: 40px;
}

.impressum-content h2:first-child {
    margin-top: 0;
}

.impressum-content h4 {
    color: #112736;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
    margin-top: 30px;
}

.impressum-content p {
    color: #04070f;
    line-height: 1.8;
    margin-bottom: 20px;
    font-size: 1rem;
}

.contact-info {
    background: #4b879a;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #112736;
    margin-bottom: 20px;
}

.contact-info p {
    margin: 0;
    color: #fff8ef;
    font-weight: 500;
}

@media (max-width: 768px) {
    .impressum-page {
        padding-top: 60px;
    }

    .impressum-content {
        margin: 20px;
        padding: 30px 20px;
    }

    .impressum-content h2 {
        font-size: 1.5rem;
    }

    .impressum-content h4 {
        font-size: 1.1rem;
    }
}
